// upload.js
Page({
  data: {
    functionType: '',
    functionName: '',
    functionDesc: '',
    functionIcon: '',
    supportedFormats: '',
    actionText: '',
    fileList: [],
    showProgress: false,
    progressText: '',
    progressPercent: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    const functionType = options.type || 'pdf-to-word'
    this.initPageData(functionType)
  },

  /**
   * 初始化页面数据
   */
  initPageData: function (functionType) {
    const functionConfig = this.getFunctionConfig(functionType)
    this.setData({
      functionType: functionType,
      functionName: functionConfig.name,
      functionDesc: functionConfig.desc,
      functionIcon: functionConfig.icon,
      supportedFormats: functionConfig.formats,
      actionText: functionConfig.action
    })
  },

  /**
   * 获取功能配置
   */
  getFunctionConfig: function (type) {
    const configs = {
      'pdf-to-word': {
        name: 'PDF转Word',
        desc: '将PDF文档转换为可编辑的Word文档',
        icon: '/images/icons/word.svg',
        formats: 'PDF',
        action: '转换'
      },
      'pdf-to-excel': {
        name: 'PDF转Excel',
        desc: '将PDF表格转换为Excel电子表格',
        icon: '/images/icons/excel.svg',
        formats: 'PDF',
        action: '转换'
      },
      'word-to-pdf': {
        name: 'Word转PDF',
        desc: '将Word文档转换为PDF格式',
        icon: '/images/icons/pdf.svg',
        formats: 'DOC, DOCX',
        action: '转换'
      },
      'ppt-to-pdf': {
        name: 'PPT转PDF',
        desc: '将PowerPoint演示文稿转换为PDF',
        icon: '/images/icons/pdf.svg',
        formats: 'PPT, PPTX',
        action: '转换'
      },
      'merge-pdf': {
        name: '合并PDF',
        desc: '将多个PDF文件合并为一个文件',
        icon: '/images/icons/合并PDF.svg',
        formats: 'PDF',
        action: '合并'
      },
      'compress-pdf': {
        name: '压缩PDF',
        desc: '减小PDF文件大小，优化存储空间',
        icon: '/images/icons/压缩PDF.svg',
        formats: 'PDF',
        action: '压缩'
      },
      'image-to-pdf': {
        name: '图片转PDF',
        desc: '将图片文件转换为PDF文档',
        icon: '/images/icons/pdf.svg',
        formats: 'JPG, PNG, BMP',
        action: '转换'
      },
      'pdf-to-image': {
        name: 'PDF转图片',
        desc: '将PDF页面转换为图片格式',
        icon: '/images/icons/jpg.svg',
        formats: 'PDF',
        action: '转换'
      }
    }
    return configs[type] || configs['pdf-to-word']
  },

  /**
   * 选择文件
   */
  chooseFile: function () {
    const that = this
    wx.chooseMessageFile({
      count: this.data.functionType === 'merge-pdf' ? 10 : 1,
      type: 'file',
      success: function (res) {
        const tempFiles = res.tempFiles
        const newFiles = tempFiles.map((file, index) => ({
          name: file.name,
          size: that.formatFileSize(file.size),
          path: file.path,
          icon: that.getFileIcon(file.name)
        }))
        
        if (that.data.functionType === 'merge-pdf') {
          // 合并PDF可以选择多个文件
          that.setData({
            fileList: [...that.data.fileList, ...newFiles]
          })
        } else {
          // 其他功能只能选择一个文件
          that.setData({
            fileList: newFiles
          })
        }
      },
      fail: function (err) {
        wx.showToast({
          title: '选择文件失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 移除文件
   */
  removeFile: function (e) {
    const index = e.currentTarget.dataset.index
    const fileList = this.data.fileList
    fileList.splice(index, 1)
    this.setData({
      fileList: fileList
    })
  },

  /**
   * 开始转换
   */
  startConversion: function () {
    if (this.data.fileList.length === 0) {
      wx.showToast({
        title: '请先选择文件',
        icon: 'none'
      })
      return
    }

    this.setData({
      showProgress: true,
      progressText: '正在处理文件...',
      progressPercent: 0
    })

    // 模拟处理进度
    this.simulateProgress()
  },

  /**
   * 模拟处理进度
   */
  simulateProgress: function () {
    const that = this
    let progress = 0
    const timer = setInterval(() => {
      progress += Math.random() * 20
      if (progress >= 100) {
        progress = 100
        clearInterval(timer)
        that.setData({
          progressPercent: progress,
          progressText: '处理完成！'
        })
        setTimeout(() => {
          that.showResult()
        }, 1000)
      } else {
        that.setData({
          progressPercent: Math.floor(progress)
        })
      }
    }, 500)
  },

  /**
   * 显示结果
   */
  showResult: function () {
    wx.showModal({
      title: '处理完成',
      content: `${this.data.functionName}已完成，文件已保存到相册`,
      showCancel: false,
      confirmText: '确定',
      success: () => {
        this.setData({
          showProgress: false,
          fileList: [],
          progressPercent: 0
        })
      }
    })
  },

  /**
   * 格式化文件大小
   */
  formatFileSize: function (bytes) {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  },

  /**
   * 获取文件图标
   */
  getFileIcon: function (fileName) {
    const ext = fileName.split('.').pop().toLowerCase()
    const iconMap = {
      'pdf': '/images/icons/pdf.svg',
      'doc': '/images/icons/word.svg',
      'docx': '/images/icons/word.svg',
      'xls': '/images/icons/excel.svg',
      'xlsx': '/images/icons/excel.svg',
      'ppt': '/images/icons/ppt.svg',
      'pptx': '/images/icons/ppt.svg',
      'jpg': '/images/icons/jpg.svg',
      'jpeg': '/images/icons/jpg.svg',
      'png': '/images/icons/png.svg',
      'bmp': '/images/icons/jpg.svg'
    }
    return iconMap[ext] || '/images/icons/pdf.svg'
  }
})
