<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF转换助手 - 预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            max-width: 375px;
            margin: 0 auto;
            min-height: 100vh;
        }
        
        .navbar {
            background: #4A90E2;
            color: white;
            text-align: center;
            padding: 20px 0;
            font-size: 18px;
            font-weight: 600;
        }
        
        .container {
            padding: 0 15px;
            padding-bottom: 20px;
        }
        
        .header-section {
            padding: 30px 0 20px 0;
            text-align: center;
        }
        
        .title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        
        .subtitle {
            font-size: 14px;
            color: #666;
        }
        
        .category-section {
            margin-bottom: 30px;
        }
        
        .category-title {
            margin-bottom: 15px;
            padding-left: 10px;
            position: relative;
        }
        
        .category-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .category-name::before {
            content: '';
            position: absolute;
            left: -10px;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 16px;
            background: linear-gradient(135deg, #4A90E2, #357ABD);
            border-radius: 2px;
        }
        
        .function-grid {
            display: flex;
            flex-direction: column;
            gap: 10px;
            align-items: center;
        }

        .function-item {
            width: 100%;
            max-width: 250px;
            background: white;
            border-radius: 8px;
            padding: 20px 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }
        
        .function-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #4A90E2, #357ABD);
        }
        
        .function-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
        }
        
        .function-icon {
            width: 50px;
            height: 50px;
            margin: 0 auto 15px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            color: #666;
            text-align: center;
            background: #f8f9fa;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }

        .function-icon.svg-icon {
            background: transparent;
            border: none;
            padding: 10px;
        }

        .icon-svg {
            width: 30px;
            height: 30px;
        }

        .function-icon.text-icon {
            background: #f0f0f0;
            border: 1px solid #ddd;
        }
        
        /* 临时保留文字图标，等待SVG替换 */
        
        .function-name {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="navbar">PDF转换助手</div>
    
    <div class="container">
        <div class="header-section">
            <div class="title">PDF转换助手</div>
            <div class="subtitle">专业的PDF处理工具</div>
        </div>
        
        <div class="category-section">
            <div class="category-title">
                <div class="category-name">格式转换</div>
            </div>
            <div class="function-grid">
                <div class="function-item">
                    <div class="function-icon pdf-icon">PDF</div>
                    <div class="function-name">PDF转Word</div>
                </div>
                <div class="function-item">
                    <div class="function-icon excel-icon">XLS</div>
                    <div class="function-name">PDF转Excel</div>
                </div>
                <div class="function-item">
                    <div class="function-icon word-icon">DOC</div>
                    <div class="function-name">Word转PDF</div>
                </div>
                <div class="function-item">
                    <div class="function-icon ppt-icon">PPT</div>
                    <div class="function-name">PPT转PDF</div>
                </div>
            </div>
        </div>
        
        <div class="category-section">
            <div class="category-title">
                <div class="category-name">PDF编辑</div>
            </div>
            <div class="function-grid">
                <div class="function-item">
                    <div class="function-icon merge-icon">合并</div>
                    <div class="function-name">合并PDF</div>
                </div>
                <div class="function-item">
                    <div class="function-icon split-icon">分割</div>
                    <div class="function-name">分割PDF</div>
                </div>
                <div class="function-item">
                    <div class="function-icon compress-icon">压缩</div>
                    <div class="function-name">压缩PDF</div>
                </div>
                <div class="function-item">
                    <div class="function-icon watermark-icon">水印</div>
                    <div class="function-name">添加水印</div>
                </div>
            </div>
        </div>
        
        <div class="category-section">
            <div class="category-title">
                <div class="category-name">图片处理</div>
            </div>
            <div class="function-grid">
                <div class="function-item">
                    <div class="function-icon image-icon">IMG</div>
                    <div class="function-name">图片转PDF</div>
                </div>
                <div class="function-item">
                    <div class="function-icon pdf-to-img-icon">转图</div>
                    <div class="function-name">PDF转图片</div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 添加点击效果
        document.querySelectorAll('.function-item').forEach(item => {
            item.addEventListener('click', function() {
                const functionName = this.querySelector('.function-name').textContent;
                alert(`${functionName}功能开发中...`);
            });
        });
    </script>
</body>
</html>
