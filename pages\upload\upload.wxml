<!--upload.wxml-->
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <!-- 头部区域 -->
    <view class="header-section">
      <view class="function-info">
        <view class="function-icon-large svg-icon">
          <image src="{{functionIcon}}" class="icon-svg-large" />
        </view>
        <text class="function-title">{{functionName}}</text>
        <text class="function-desc">{{functionDesc}}</text>
      </view>
    </view>

    <!-- 上传区域 -->
    <view class="upload-section">
      <view class="upload-area" bindtap="chooseFile">
        <view class="upload-icon">
          <image src="/images/icons/上传1.svg" class="upload-svg" />
        </view>
        <view class="upload-text">
          <text class="upload-title">点击选择文件</text>
          <text class="upload-subtitle">支持{{supportedFormats}}格式</text>
        </view>
      </view>
    </view>

    <!-- 文件列表 -->
    <view class="file-list" wx:if="{{fileList.length > 0}}">
      <view class="list-title">
        <text class="title-text">已选择文件</text>
        <text class="file-count">({{fileList.length}})</text>
      </view>
      <view class="file-items">
        <view class="file-item" wx:for="{{fileList}}" wx:key="index">
          <view class="file-info">
            <view class="file-icon">
              <image src="{{item.icon}}" class="file-type-icon" />
            </view>
            <view class="file-details">
              <text class="file-name">{{item.name}}</text>
              <text class="file-size">{{item.size}}</text>
            </view>
          </view>
          <view class="file-actions">
            <view class="remove-btn" bindtap="removeFile" data-index="{{index}}">
              <text>删除</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <button class="btn btn-primary action-btn" bindtap="startConversion" disabled="{{fileList.length === 0}}">
        开始{{actionText}}
      </button>
    </view>

    <!-- 进度显示 -->
    <view class="progress-section" wx:if="{{showProgress}}">
      <view class="progress-info">
        <text class="progress-text">{{progressText}}</text>
        <text class="progress-percent">{{progressPercent}}%</text>
      </view>
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{progressPercent}}%"></view>
      </view>
    </view>
  </view>
</scroll-view>
