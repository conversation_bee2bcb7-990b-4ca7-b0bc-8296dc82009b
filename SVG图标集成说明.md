# SVG图标集成说明

## 📋 需要的图标列表

根据vscode-icons插件风格，你需要找到以下图标的SVG文件：

### 格式转换类
1. **PDF文件图标** - 用于"PDF转Word"功能
2. **Excel文件图标** - 用于"PDF转Excel"功能  
3. **Word文件图标** - 用于"Word转PDF"功能
4. **PowerPoint文件图标** - 用于"PPT转PDF"功能

### PDF编辑类
5. **合并图标** - 用于"合并PDF"功能（可能是两个文档合并的图标）
6. **分割图标** - 用于"分割PDF"功能（可能是文档分割的图标）
7. **压缩图标** - 用于"压缩PDF"功能（可能是压缩/打包图标）
8. **水印图标** - 用于"添加水印"功能（可能是印章或标记图标）

### 图片处理类
9. **图片文件图标** - 用于"图片转PDF"功能
10. **转换图标** - 用于"PDF转图片"功能

## 🔧 集成步骤

### 1. 准备SVG文件
- 将SVG文件放在 `images/icons/` 目录下
- 建议文件命名：
  - `pdf.svg` - PDF图标
  - `excel.svg` - Excel图标
  - `word.svg` - Word图标
  - `powerpoint.svg` - PowerPoint图标
  - `merge.svg` - 合并图标
  - `split.svg` - 分割图标
  - `compress.svg` - 压缩图标
  - `watermark.svg` - 水印图标
  - `image.svg` - 图片图标
  - `convert.svg` - 转换图标

### 2. 在WXML中使用SVG
替换现有的文字图标，例如：

```xml
<!-- 原来的文字图标 -->
<view class="function-icon pdf-icon">
  <text>PDF</text>
</view>

<!-- 替换为SVG图标 -->
<view class="function-icon svg-icon">
  <image src="/images/icons/pdf.svg" class="icon-svg" />
</view>
```

### 3. 更新CSS样式
在 `index.wxss` 中添加SVG图标样式：

```css
.icon-svg {
  width: 60rpx;
  height: 60rpx;
}

.function-icon.svg-icon {
  background: transparent;
  border: none;
  padding: 20rpx;
}
```

## 📁 推荐的图标资源

### vscode-icons相关资源
1. **GitHub仓库**: https://github.com/vscode-icons/vscode-icons
2. **图标预览**: 可以在VSCode中查看各种文件类型的图标
3. **SVG文件位置**: 通常在 `icons/` 目录下

### 其他优质图标库
1. **Heroicons**: https://heroicons.com/
2. **Lucide**: https://lucide.dev/
3. **Tabler Icons**: https://tabler-icons.io/
4. **Feather Icons**: https://feathericons.com/

## 🎨 图标样式建议

- **尺寸**: 建议使用60rpx x 60rpx
- **颜色**: 保持原有颜色或使用单色（#666）
- **风格**: 保持一致的线条粗细和圆角风格
- **格式**: 使用SVG格式以保证清晰度

## 📝 当前状态

✅ 布局已调整为居中显示
✅ SVG图标已集成到项目中
✅ CSS样式已完善
✅ 已有图标的功能已使用SVG
⚠️ 部分功能暂时使用文字图标

## 📊 图标使用情况

### ✅ 已使用SVG图标
- PDF转Word → word.svg
- PDF转Excel → excel.svg
- Word转PDF → pdf.svg
- PPT转PDF → pdf.svg
- 图片转PDF → pdf.svg
- PDF转图片 → jpg.svg

### ⏳ 仍需补充图标
- 合并PDF → 需要合并类图标
- 分割PDF → 需要分割类图标
- 压缩PDF → 需要压缩类图标
- 添加水印 → 需要水印类图标

## 🚀 下一步

1. 寻找合并、分割、压缩、水印相关的SVG图标
2. 替换剩余的文字图标
3. 测试所有图标显示效果
4. 开始开发具体功能页面
