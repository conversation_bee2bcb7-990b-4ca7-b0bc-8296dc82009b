/**app.wxss**/

/* 全局样式重置 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
}

/* 全局容器样式 */
.container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* 通用按钮样式 */
.btn {
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #4A90E2, #357ABD);
  color: white;
}

.btn-primary:active {
  background: linear-gradient(135deg, #357ABD, #2E6DA4);
}

/* 通用卡片样式 */
.card {
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

/* 通用文本样式 */
.text-primary {
  color: #4A90E2;
}

.text-secondary {
  color: #666;
}

.text-muted {
  color: #999;
}

/* 通用间距 */
.mt-small { margin-top: 20rpx; }
.mt-medium { margin-top: 40rpx; }
.mt-large { margin-top: 60rpx; }

.mb-small { margin-bottom: 20rpx; }
.mb-medium { margin-bottom: 40rpx; }
.mb-large { margin-bottom: 60rpx; }

.p-small { padding: 20rpx; }
.p-medium { padding: 40rpx; }
.p-large { padding: 60rpx; }
