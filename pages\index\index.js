// index.js
Page({
  data: {
    // 功能列表数据
    functionList: [
      {
        category: '格式转换',
        functions: [
          { type: 'pdf-to-word', name: 'PDF转Word', icon: 'PDF' },
          { type: 'pdf-to-excel', name: 'PDF转Excel', icon: 'XLS' },
          { type: 'word-to-pdf', name: 'Word转PDF', icon: 'DOC' },
          { type: 'ppt-to-pdf', name: 'PPT转PDF', icon: 'PPT' }
        ]
      },
      {
        category: 'PDF编辑',
        functions: [
          { type: 'merge-pdf', name: '合并PDF', icon: '合并' },
          { type: 'split-pdf', name: '分割PDF', icon: '分割' },
          { type: 'compress-pdf', name: '压缩PDF', icon: '压缩' },
          { type: 'watermark', name: '添加水印', icon: '水印' }
        ]
      },
      {
        category: '图片处理',
        functions: [
          { type: 'image-to-pdf', name: '图片转PDF', icon: 'IMG' },
          { type: 'pdf-to-image', name: 'PDF转图片', icon: '转图' }
        ]
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('PDF转换助手页面加载完成')
  },

  /**
   * 功能点击事件处理
   */
  onFunctionTap: function (e) {
    const functionType = e.currentTarget.dataset.type
    console.log('点击功能:', functionType)

    // 跳转到上传页面
    wx.navigateTo({
      url: `/pages/upload/upload?type=${functionType}`
    })
  },



  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: 'PDF转换助手 - 专业的PDF处理工具',
      path: '/pages/index/index'
    }
  }
})
