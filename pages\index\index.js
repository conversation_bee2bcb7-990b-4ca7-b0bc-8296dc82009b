// index.js
Page({
  data: {
    // 功能列表数据
    functionList: [
      {
        category: '格式转换',
        functions: [
          { type: 'pdf-to-word', name: 'PDF转Word', icon: 'PDF' },
          { type: 'pdf-to-excel', name: 'PDF转Excel', icon: 'XLS' },
          { type: 'word-to-pdf', name: 'Word转PDF', icon: 'DOC' },
          { type: 'ppt-to-pdf', name: 'PPT转PDF', icon: 'PPT' }
        ]
      },
      {
        category: 'PDF编辑',
        functions: [
          { type: 'merge-pdf', name: '合并PDF', icon: '合并' },
          { type: 'split-pdf', name: '分割PDF', icon: '分割' },
          { type: 'compress-pdf', name: '压缩PDF', icon: '压缩' },
          { type: 'watermark', name: '添加水印', icon: '水印' }
        ]
      },
      {
        category: '图片处理',
        functions: [
          { type: 'image-to-pdf', name: '图片转PDF', icon: 'IMG' },
          { type: 'pdf-to-image', name: 'PDF转图片', icon: '转图' }
        ]
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('PDF转换助手页面加载完成')
  },

  /**
   * 功能点击事件处理
   */
  onFunctionTap: function (e) {
    const functionType = e.currentTarget.dataset.type
    console.log('点击功能:', functionType)

    // 根据功能类型进行不同处理
    switch (functionType) {
      case 'pdf-to-word':
        this.handlePdfToWord()
        break
      case 'pdf-to-excel':
        this.handlePdfToExcel()
        break
      case 'word-to-pdf':
        this.handleWordToPdf()
        break
      case 'ppt-to-pdf':
        this.handlePptToPdf()
        break
      case 'merge-pdf':
        this.handleMergePdf()
        break
      case 'split-pdf':
        this.handleSplitPdf()
        break
      case 'compress-pdf':
        this.handleCompressPdf()
        break
      case 'watermark':
        this.handleWatermark()
        break
      case 'image-to-pdf':
        this.handleImageToPdf()
        break
      case 'pdf-to-image':
        this.handlePdfToImage()
        break
      default:
        this.showComingSoon()
    }
  },

  /**
   * PDF转Word功能
   */
  handlePdfToWord: function () {
    wx.showToast({
      title: 'PDF转Word功能开发中',
      icon: 'none',
      duration: 2000
    })
  },

  /**
   * PDF转Excel功能
   */
  handlePdfToExcel: function () {
    wx.showToast({
      title: 'PDF转Excel功能开发中',
      icon: 'none',
      duration: 2000
    })
  },

  /**
   * Word转PDF功能
   */
  handleWordToPdf: function () {
    wx.showToast({
      title: 'Word转PDF功能开发中',
      icon: 'none',
      duration: 2000
    })
  },

  /**
   * PPT转PDF功能
   */
  handlePptToPdf: function () {
    wx.showToast({
      title: 'PPT转PDF功能开发中',
      icon: 'none',
      duration: 2000
    })
  },

  /**
   * 合并PDF功能
   */
  handleMergePdf: function () {
    wx.showToast({
      title: '合并PDF功能开发中',
      icon: 'none',
      duration: 2000
    })
  },

  /**
   * 分割PDF功能
   */
  handleSplitPdf: function () {
    wx.showToast({
      title: '分割PDF功能开发中',
      icon: 'none',
      duration: 2000
    })
  },

  /**
   * 压缩PDF功能
   */
  handleCompressPdf: function () {
    wx.showToast({
      title: '压缩PDF功能开发中',
      icon: 'none',
      duration: 2000
    })
  },

  /**
   * 添加水印功能
   */
  handleWatermark: function () {
    wx.showToast({
      title: '添加水印功能开发中',
      icon: 'none',
      duration: 2000
    })
  },

  /**
   * 图片转PDF功能
   */
  handleImageToPdf: function () {
    wx.showToast({
      title: '图片转PDF功能开发中',
      icon: 'none',
      duration: 2000
    })
  },

  /**
   * PDF转图片功能
   */
  handlePdfToImage: function () {
    wx.showToast({
      title: 'PDF转图片功能开发中',
      icon: 'none',
      duration: 2000
    })
  },

  /**
   * 显示功能即将上线提示
   */
  showComingSoon: function () {
    wx.showToast({
      title: '功能即将上线',
      icon: 'none',
      duration: 2000
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: 'PDF转换助手 - 专业的PDF处理工具',
      path: '/pages/index/index'
    }
  }
})
