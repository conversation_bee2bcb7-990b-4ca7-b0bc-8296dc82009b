<!-- index.wxml -->
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <!-- 头部欢迎区域 -->
    <view class="header-section">
      <view class="welcome-text">
        <text class="title">PDF转换助手(<PERSON>)</text>
        <text class="subtitle">无任何广告,无需任何登录的PDF处理工具</text>
      </view>
    </view>
    <!-- 功能分类区域 -->
    <view class="function-categories">
      <!-- 格式转换 -->
      <view class="category-section">
        <view class="category-title">
          <text class="category-name">格式转换</text>
        </view>
        <view class="function-grid">
          <view class="function-item" bindtap="onFunctionTap" data-type="pdf-to-word">
            <view class="function-icon svg-icon">
              <image src="/images/icons/word.svg" class="icon-svg" />
            </view>
            <text class="function-name">PDF转Word</text>
          </view>
          <view class="function-item" bindtap="onFunctionTap" data-type="pdf-to-excel">
            <view class="function-icon svg-icon">
              <image src="/images/icons/excel.svg" class="icon-svg" />
            </view>
            <text class="function-name">PDF转Excel</text>
          </view>
          <view class="function-item" bindtap="onFunctionTap" data-type="word-to-pdf">
            <view class="function-icon svg-icon">
              <image src="/images/icons/pdf.svg" class="icon-svg" />
            </view>
            <text class="function-name">Word转PDF</text>
          </view>
          <view class="function-item" bindtap="onFunctionTap" data-type="ppt-to-pdf">
            <view class="function-icon svg-icon">
              <image src="/images/icons/pdf.svg" class="icon-svg" />
            </view>
            <text class="function-name">PPT转PDF</text>
          </view>
        </view>
      </view>
      <!-- PDF编辑 -->
      <view class="category-section">
        <view class="category-title">
          <text class="category-name">PDF编辑</text>
        </view>
        <view class="function-grid">
          <view class="function-item" bindtap="onFunctionTap" data-type="merge-pdf">
            <view class="function-icon svg-icon pdf-edit-icon">
              <image src="/images/icons/合并PDF.svg" class="icon-svg-large" />
            </view>
            <text class="function-name">合并PDF</text>
          </view>
          <view class="function-item" bindtap="onFunctionTap" data-type="compress-pdf">
            <view class="function-icon svg-icon pdf-edit-icon">
              <image src="/images/icons/压缩PDF.svg" class="icon-svg-large" />
            </view>
            <text class="function-name">压缩PDF</text>
          </view>
        </view>
      </view>
      <!-- 图片处理 -->
      <view class="category-section">
        <view class="category-title">
          <text class="category-name">图片处理</text>
        </view>
        <view class="function-grid">
          <view class="function-item" bindtap="onFunctionTap" data-type="image-to-pdf">
            <view class="function-icon svg-icon">
              <image src="/images/icons/pdf.svg" class="icon-svg" />
            </view>
            <text class="function-name">图片转PDF</text>
          </view>
          <view class="function-item" bindtap="onFunctionTap" data-type="pdf-to-image">
            <view class="function-icon svg-icon">
              <image src="/images/icons/jpg.svg" class="icon-svg" />
            </view>
            <text class="function-name">PDF转图片</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</scroll-view>