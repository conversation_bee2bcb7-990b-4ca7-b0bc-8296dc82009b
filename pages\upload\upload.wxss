/**upload.wxss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  padding: 0 30rpx;
  padding-bottom: 40rpx;
}

/* 头部区域 */
.header-section {
  padding: 40rpx 0;
  text-align: center;
}

.function-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.function-icon-large {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.icon-svg-large {
  width: 80rpx;
  height: 80rpx;
}

.function-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.function-desc {
  font-size: 26rpx;
  color: #666;
}

/* 上传区域 */
.upload-section {
  margin: 40rpx 0;
}

.upload-area {
  background: white;
  border: 3rpx dashed #4A90E2;
  border-radius: 16rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.upload-area:active {
  background: #f8f9ff;
  border-color: #357ABD;
}

.upload-icon {
  margin-bottom: 30rpx;
}

.upload-svg {
  width: 100rpx;
  height: 100rpx;
}

.upload-text {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #4A90E2;
  margin-bottom: 10rpx;
}

.upload-subtitle {
  font-size: 26rpx;
  color: #666;
}

/* 文件列表 */
.file-list {
  margin: 40rpx 0;
}

.list-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding-left: 20rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  position: relative;
}

.title-text::before {
  content: '';
  position: absolute;
  left: -20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #4A90E2, #357ABD);
  border-radius: 4rpx;
}

.file-count {
  font-size: 26rpx;
  color: #666;
  margin-left: 10rpx;
}

.file-items {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.file-item {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.file-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 10rpx;
}

.file-type-icon {
  width: 40rpx;
  height: 40rpx;
}

.file-details {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.file-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 5rpx;
  word-break: break-all;
}

.file-size {
  font-size: 24rpx;
  color: #666;
}

.file-actions {
  margin-left: 20rpx;
}

.remove-btn {
  background: #ff4757;
  color: white;
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.remove-btn:active {
  background: #ff3742;
}

/* 操作按钮 */
.action-section {
  margin: 40rpx 0;
}

.action-btn {
  width: 100%;
  height: 88rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.action-btn[disabled] {
  background: #ccc !important;
  color: #999 !important;
}

/* 进度显示 */
.progress-section {
  margin: 40rpx 0;
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.progress-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.progress-percent {
  font-size: 28rpx;
  color: #4A90E2;
  font-weight: bold;
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background: #e9ecef;
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4A90E2, #357ABD);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}
