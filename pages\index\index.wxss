/**index.wxss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  padding: 0 30rpx;
  padding-bottom: 40rpx;
}

/* 头部区域 */
.header-section {
  padding: 120rpx 0 40rpx 0;
  text-align: center;
}

.welcome-text .title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.welcome-text .subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

/* 功能分类区域 */
.function-categories {
  margin-top: 20rpx;
}

.category-section {
  margin-bottom: 60rpx;
}

.category-title {
  margin-bottom: 30rpx;
  padding-left: 20rpx;
}

.category-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  position: relative;
}

.category-name::before {
  content: '';
  position: absolute;
  left: -20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #4A90E2, #357ABD);
  border-radius: 4rpx;
}

/* 功能网格 */
.function-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  align-items: center;
}

.function-item {
  width: 100%;
  max-width: 500rpx;
  background: white;
  border-radius: 16rpx;
  padding: 40rpx 20rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.function-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #4A90E2, #357ABD);
}

.function-item:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.15);
}

/* 功能图标 */
.function-icon {
  width: 100rpx;
  height: 100rpx;
  margin: 0 auto 30rpx auto;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  color: #666;
  text-align: center;
  line-height: 1;
  background: #f8f9fa;
  border-radius: 20rpx;
  border: 2rpx solid #e9ecef;
}

/* SVG图标样式 */
.function-icon.svg-icon {
  background: transparent;
  border: none;
  padding: 20rpx;
}

.icon-svg {
  width: 60rpx;
  height: 60rpx;
}

/* PDF编辑功能的大图标 */
.icon-svg-large {
  width: 80rpx;
  height: 80rpx;
}

/* PDF编辑图标容器调整 */
.pdf-edit-icon {
  width: 120rpx;
  height: 120rpx;
  padding: 20rpx;
}

/* 文字图标样式 - 用于暂时没有SVG的功能 */
.function-icon.text-icon {
  background: #f0f0f0;
  border: 2rpx solid #ddd;
  color: #666;
  font-size: 24rpx;
  font-weight: bold;
}

/* 功能名称 */
.function-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
